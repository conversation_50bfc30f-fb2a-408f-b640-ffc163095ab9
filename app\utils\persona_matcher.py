"""
Persona Matcher - Intelligent persona selection based on user profile analysis
"""
import re
from typing import Dict, List, Any, Tuple
from collections import defaultdict

# Define the 22 available personas with their matching criteria
AVAILABLE_PERSONAS = {
    "AI Engineer": {
        "keywords": [
            "artificial intelligence", "machine learning", "deep learning", "neural networks",
            "ai", "ml", "tensorflow", "pytorch", "python", "data science", "computer vision",
            "nlp", "natural language processing", "algorithms", "model training", "automation",
            "ai engineer", "machine learning engineer", "research", "opencv", "scikit-learn"
        ],
        "job_titles": [
            "ai engineer", "machine learning engineer", "ml engineer", "artificial intelligence engineer",
            "data scientist", "research scientist", "ai researcher", "deep learning engineer",
            "computer vision engineer", "nlp engineer", "ai specialist"
        ],
        "industries": ["artificial intelligence", "machine learning", "technology", "research", "data science"]
    },
    
    "Android Developer": {
        "keywords": [
            "android", "mobile development", "kotlin", "java", "android studio", "mobile app",
            "app development", "google play", "firebase", "jetpack compose", "mvvm", "retrofit",
            "room database", "material design", "mobile developer"
        ],
        "job_titles": [
            "android developer", "mobile developer", "mobile app developer", "android engineer",
            "mobile software engineer", "app developer", "kotlin developer", "mobile application developer"
        ],
        "industries": ["mobile", "app development", "technology", "mobile technology"]
    },
    
    "B2B Sales Executive": {
        "keywords": [
            "b2b sales", "business development", "sales", "account management", "lead generation",
            "crm", "salesforce", "pipeline management", "client relations", "revenue growth",
            "sales strategy", "enterprise sales", "prospecting", "closing deals", "account executive"
        ],
        "job_titles": [
            "sales executive", "b2b sales executive", "account executive", "business development manager",
            "sales manager", "senior sales executive", "enterprise sales executive", "sales director",
            "business development executive", "account manager", "sales representative"
        ],
        "industries": ["sales", "business development", "saas", "enterprise software", "consulting"]
    },
    
    "Backend Engineer": {
        "keywords": [
            "backend", "server-side", "api", "database", "microservices", "cloud", "aws", "docker",
            "kubernetes", "python", "java", "node.js", "sql", "nosql", "rest api", "graphql",
            "backend developer", "server development", "system architecture"
        ],
        "job_titles": [
            "backend engineer", "backend developer", "server-side developer", "api developer",
            "software engineer", "full stack developer", "system engineer", "cloud engineer"
        ],
        "industries": ["technology", "software", "cloud computing", "web development"]
    },
    
    "Civil Manager": {
        "keywords": [
            "civil engineering", "construction", "project management", "infrastructure", "structural",
            "building", "construction management", "site management", "engineering", "architecture",
            "civil", "construction project", "site supervisor", "project coordination"
        ],
        "job_titles": [
            "civil manager", "construction manager", "project manager", "site manager",
            "civil engineer", "construction project manager", "engineering manager", "site supervisor"
        ],
        "industries": ["construction", "civil engineering", "infrastructure", "real estate", "engineering"]
    },

    "C-Level Executive": {
        "keywords": [
            "ceo", "cto", "cfo", "coo", "chief", "executive", "leadership", "strategy", "vision",
            "board", "c-suite", "senior leadership", "executive management", "corporate strategy",
            "business strategy", "organizational leadership", "executive director"
        ],
        "job_titles": [
            "ceo", "cto", "cfo", "coo", "chief executive officer", "chief technology officer",
            "chief financial officer", "chief operating officer", "president", "vice president",
            "executive director", "managing director", "founder"
        ],
        "industries": ["executive", "leadership", "management", "corporate", "business"]
    },

    "Content Creator": {
        "keywords": [
            "content creation", "content marketing", "social media", "blogging", "writing",
            "copywriting", "content strategy", "digital marketing", "storytelling", "brand content",
            "content creator", "influencer", "video content", "podcast", "youtube", "instagram"
        ],
        "job_titles": [
            "content creator", "content manager", "content marketing manager", "social media manager",
            "copywriter", "content strategist", "digital content creator", "brand content manager",
            "content writer", "marketing content specialist"
        ],
        "industries": ["marketing", "digital marketing", "media", "advertising", "content marketing"]
    },

    "Data Scientist": {
        "keywords": [
            "data science", "data analysis", "statistics", "python", "r", "sql", "machine learning",
            "data mining", "big data", "analytics", "data visualization", "pandas", "numpy",
            "tableau", "power bi", "statistical analysis", "predictive modeling", "data scientist"
        ],
        "job_titles": [
            "data scientist", "data analyst", "senior data scientist", "lead data scientist",
            "data science manager", "business analyst", "quantitative analyst", "research analyst",
            "data engineer", "analytics manager"
        ],
        "industries": ["data science", "analytics", "technology", "finance", "healthcare", "consulting"]
    },

    "Entrepreneur": {
        "keywords": [
            "entrepreneur", "startup", "founder", "business owner", "entrepreneurship", "innovation",
            "venture capital", "business development", "startup founder", "ceo", "co-founder",
            "small business", "business strategy", "scaling", "growth hacking", "bootstrapping"
        ],
        "job_titles": [
            "entrepreneur", "founder", "co-founder", "startup founder", "business owner", "ceo",
            "startup ceo", "serial entrepreneur", "social entrepreneur", "tech entrepreneur"
        ],
        "industries": ["startup", "entrepreneurship", "venture capital", "innovation", "business"]
    },

    "Financial Analyst": {
        "keywords": [
            "financial analysis", "finance", "investment", "financial modeling", "excel", "valuation",
            "budgeting", "forecasting", "financial planning", "accounting", "financial reporting",
            "investment analysis", "portfolio management", "risk analysis", "financial analyst"
        ],
        "job_titles": [
            "financial analyst", "senior financial analyst", "investment analyst", "budget analyst",
            "financial planning analyst", "business analyst", "credit analyst", "research analyst",
            "portfolio analyst", "finance manager"
        ],
        "industries": ["finance", "banking", "investment", "consulting", "insurance", "corporate finance"]
    },

    "Freelancer Independent Consultant": {
        "keywords": [
            "freelancer", "consultant", "independent", "consulting", "freelance", "contractor",
            "self-employed", "independent contractor", "business consultant", "freelance writer",
            "freelance developer", "independent consultant", "contract work", "remote work"
        ],
        "job_titles": [
            "freelancer", "independent consultant", "consultant", "freelance developer",
            "freelance writer", "freelance designer", "independent contractor", "business consultant",
            "freelance marketer", "contract specialist"
        ],
        "industries": ["consulting", "freelance", "independent", "contract work", "services"]
    },

    "Graphic Designer": {
        "keywords": [
            "graphic design", "design", "adobe", "photoshop", "illustrator", "indesign", "creative",
            "visual design", "branding", "logo design", "typography", "layout", "print design",
            "digital design", "creative director", "art director", "graphic designer"
        ],
        "job_titles": [
            "graphic designer", "visual designer", "creative director", "art director",
            "senior graphic designer", "brand designer", "digital designer", "ui designer",
            "print designer", "freelance designer"
        ],
        "industries": ["design", "advertising", "marketing", "media", "creative services", "branding"]
    },

    "Growth Manager": {
        "keywords": [
            "growth", "growth hacking", "user acquisition", "retention", "analytics", "a/b testing",
            "conversion optimization", "product growth", "marketing", "growth strategy", "metrics",
            "kpi", "growth manager", "growth marketing", "user engagement", "funnel optimization"
        ],
        "job_titles": [
            "growth manager", "growth marketing manager", "growth hacker", "user acquisition manager",
            "growth product manager", "marketing manager", "digital marketing manager", "growth analyst",
            "growth strategist", "performance marketing manager"
        ],
        "industries": ["technology", "saas", "startup", "e-commerce", "digital marketing", "mobile apps"]
    },

    "HR Manager": {
        "keywords": [
            "human resources", "hr", "recruitment", "talent acquisition", "employee relations",
            "hr management", "people operations", "organizational development", "performance management",
            "compensation", "benefits", "hr strategy", "workforce planning", "employee engagement"
        ],
        "job_titles": [
            "hr manager", "human resources manager", "hr director", "people manager", "hr business partner",
            "talent manager", "hr generalist", "people operations manager", "hr specialist",
            "organizational development manager"
        ],
        "industries": ["human resources", "hr", "consulting", "corporate", "staffing", "recruitment"]
    },

    "Job Seeker": {
        "keywords": [
            "job search", "career change", "looking for opportunities", "seeking employment",
            "career transition", "job hunting", "open to work", "available", "seeking new role",
            "career development", "professional development", "networking", "job seeker"
        ],
        "job_titles": [
            "job seeker", "career changer", "recent graduate", "professional", "candidate",
            "looking for work", "seeking opportunities", "available for hire"
        ],
        "industries": ["various", "multiple", "open", "seeking", "transitioning"]
    },

    "Marketing Director": {
        "keywords": [
            "marketing", "digital marketing", "brand management", "marketing strategy", "campaigns",
            "advertising", "content marketing", "social media marketing", "lead generation",
            "marketing director", "brand marketing", "product marketing", "growth marketing", "seo", "sem"
        ],
        "job_titles": [
            "marketing director", "marketing manager", "brand manager", "digital marketing manager",
            "marketing lead", "head of marketing", "chief marketing officer", "marketing strategist",
            "product marketing manager", "content marketing manager"
        ],
        "industries": ["marketing", "advertising", "digital marketing", "technology", "e-commerce", "saas"]
    },

    "Product Manager": {
        "keywords": [
            "product management", "product strategy", "roadmap", "user experience", "agile", "scrum",
            "product development", "feature prioritization", "stakeholder management", "product analytics",
            "product manager", "product owner", "product marketing", "user research", "mvp"
        ],
        "job_titles": [
            "product manager", "senior product manager", "product owner", "product director",
            "head of product", "product lead", "associate product manager", "principal product manager",
            "product marketing manager", "technical product manager"
        ],
        "industries": ["technology", "software", "saas", "mobile", "e-commerce", "fintech"]
    },

    "Project Manager": {
        "keywords": [
            "project management", "pmp", "agile", "scrum", "waterfall", "project planning",
            "risk management", "stakeholder management", "project coordination", "project manager",
            "program management", "project delivery", "timeline management", "budget management"
        ],
        "job_titles": [
            "project manager", "senior project manager", "program manager", "project coordinator",
            "project lead", "scrum master", "agile coach", "delivery manager", "pmo manager",
            "technical project manager"
        ],
        "industries": ["technology", "construction", "consulting", "healthcare", "finance", "manufacturing"]
    },

    "Publishing Manager (Games Marketing)": {
        "keywords": [
            "publishing", "games", "gaming", "game marketing", "mobile games", "game publishing",
            "user acquisition", "app store optimization", "game analytics", "monetization",
            "game industry", "mobile gaming", "f2p", "game promotion", "publishing manager"
        ],
        "job_titles": [
            "publishing manager", "game marketing manager", "mobile game publisher", "game producer",
            "user acquisition manager", "game marketing director", "publishing director",
            "mobile games manager", "game business manager", "publishing specialist"
        ],
        "industries": ["gaming", "mobile games", "entertainment", "digital media", "app publishing"]
    },

    "Senior SQA Engineer": {
        "keywords": [
            "quality assurance", "qa", "sqa", "testing", "automation testing", "manual testing",
            "test automation", "selenium", "quality control", "bug tracking", "test planning",
            "software testing", "qa engineer", "test engineer", "quality engineer", "regression testing"
        ],
        "job_titles": [
            "sqa engineer", "senior sqa engineer", "qa engineer", "quality assurance engineer",
            "test engineer", "automation engineer", "quality engineer", "senior qa engineer",
            "test automation engineer", "software tester"
        ],
        "industries": ["technology", "software", "quality assurance", "testing", "it services"]
    },

    "Talent Acquisition Specialist": {
        "keywords": [
            "talent acquisition", "recruitment", "recruiting", "hiring", "sourcing", "hr",
            "talent sourcing", "candidate screening", "interview", "talent management",
            "recruiter", "headhunting", "talent acquisition specialist", "recruitment consultant"
        ],
        "job_titles": [
            "talent acquisition specialist", "recruiter", "senior recruiter", "talent acquisition manager",
            "recruitment specialist", "talent sourcer", "hiring manager", "recruitment consultant",
            "talent acquisition partner", "senior talent acquisition specialist"
        ],
        "industries": ["recruitment", "human resources", "staffing", "consulting", "talent acquisition"]
    },

    "UI UX Designer": {
        "keywords": [
            "ui design", "ux design", "user interface", "user experience", "design", "figma", "sketch",
            "adobe xd", "prototyping", "wireframing", "user research", "usability", "interaction design",
            "visual design", "ui/ux designer", "product design", "web design", "mobile design"
        ],
        "job_titles": [
            "ui ux designer", "ux designer", "ui designer", "product designer", "user experience designer",
            "user interface designer", "senior ux designer", "senior ui designer", "design lead",
            "interaction designer", "visual designer"
        ],
        "industries": ["design", "technology", "software", "mobile", "web development", "digital agencies"]
    }
}

def normalize_text(text: str) -> str:
    """Normalize text for matching by converting to lowercase and removing special characters"""
    if not text:
        return ""
    return re.sub(r'[^\w\s]', ' ', text.lower()).strip()

def extract_keywords_from_profile(user_profile: Dict[str, Any]) -> List[str]:
    """Extract all relevant keywords from user profile for matching"""
    keywords = []
    
    # Extract from various profile fields
    fields_to_extract = ['headline', 'summary', 'current_role', 'industry']
    for field in fields_to_extract:
        if field in user_profile and user_profile[field]:
            keywords.extend(normalize_text(user_profile[field]).split())
    
    # Extract from work experience
    if 'work_experience' in user_profile:
        for exp in user_profile['work_experience']:
            if isinstance(exp, dict):
                for field in ['position', 'job_description', 'industry']:
                    if field in exp and exp[field]:
                        keywords.extend(normalize_text(exp[field]).split())
    
    # Extract from skills
    if 'skills' in user_profile and isinstance(user_profile['skills'], list):
        for skill in user_profile['skills']:
            if skill:
                keywords.extend(normalize_text(skill).split())
    
    # Extract from education
    if 'education' in user_profile and isinstance(user_profile['education'], list):
        for edu in user_profile['education']:
            if isinstance(edu, dict) and 'degree' in edu and edu['degree']:
                keywords.extend(normalize_text(edu['degree']).split())
    
    return [kw for kw in keywords if len(kw) > 2]  # Filter out very short words

def calculate_persona_score(user_profile: Dict[str, Any], persona_data: Dict[str, Any]) -> float:
    """Calculate how well a persona matches the user profile"""
    score = 0.0
    
    # Extract user profile keywords
    user_keywords = extract_keywords_from_profile(user_profile)
    user_text = ' '.join(user_keywords)
    
    # Check job title matches (highest weight)
    current_role = normalize_text(user_profile.get('current_role', ''))
    work_experience = user_profile.get('work_experience', [])
    
    job_title_matches = 0
    for job_title in persona_data.get('job_titles', []):
        normalized_job_title = normalize_text(job_title)
        job_title_words = normalized_job_title.split()

        # Check current role - prioritize exact matches and meaningful word matches
        if normalized_job_title in current_role:
            job_title_matches += 5  # Exact match gets highest score
        else:
            # Count meaningful word matches (ignore common words like "engineer", "manager", "senior")
            common_words = {'engineer', 'manager', 'senior', 'lead', 'director', 'specialist', 'analyst'}
            meaningful_matches = 0
            for word in job_title_words:
                if len(word) > 3 and word not in common_words and word in current_role:
                    meaningful_matches += 1

            # Give points for meaningful matches, but less than exact matches
            if meaningful_matches > 0:
                job_title_matches += meaningful_matches * 2
            # Give minimal points for common word matches
            elif any(word in current_role for word in job_title_words if word in common_words):
                job_title_matches += 0.5

        # Check work experience positions with same logic
        for exp in work_experience:
            if isinstance(exp, dict) and 'position' in exp:
                position = normalize_text(exp['position'])
                if normalized_job_title in position:
                    job_title_matches += 3  # Exact match in experience
                else:
                    meaningful_matches = 0
                    for word in job_title_words:
                        if len(word) > 3 and word not in common_words and word in position:
                            meaningful_matches += 1
                    if meaningful_matches > 0:
                        job_title_matches += meaningful_matches * 1.5
                    elif any(word in position for word in job_title_words if word in common_words):
                        job_title_matches += 0.3

    score += job_title_matches * 10  # Job titles have highest weight
    
    # Check industry matches (high weight - very important for accurate matching)
    user_industry = normalize_text(user_profile.get('industry', ''))
    industry_matches = 0
    for industry in persona_data.get('industries', []):
        normalized_industry = normalize_text(industry)
        if normalized_industry in user_industry:
            industry_matches += 3  # Exact industry match gets high score
        elif any(word in user_industry for word in normalized_industry.split() if len(word) > 3):
            industry_matches += 2  # Partial industry match gets medium score

    score += industry_matches * 15  # Industry matches have high weight (increased from 5 to 15)
    
    # Check keyword matches (lower weight but comprehensive)
    keyword_matches = 0
    for keyword in persona_data.get('keywords', []):
        normalized_keyword = normalize_text(keyword)
        if normalized_keyword in user_text:
            keyword_matches += 1
        # Also check for partial matches
        elif any(word in user_text for word in normalized_keyword.split() if len(word) > 3):
            keyword_matches += 0.5
    
    score += keyword_matches * 2  # Keywords have lower weight
    
    return score

def select_best_persona(user_profile: Dict[str, Any]) -> str:
    """
    Analyze user profile and select the best matching persona from the 22 available personas
    
    Args:
        user_profile: Dictionary containing user profile information
        
    Returns:
        str: The name of the best matching persona (e.g., "AI Engineer", "Product Manager")
    """
    if not user_profile:
        return "Job Seeker"  # Default fallback
    
    persona_scores = {}
    
    # Calculate scores for each persona
    for persona_name, persona_data in AVAILABLE_PERSONAS.items():
        score = calculate_persona_score(user_profile, persona_data)
        persona_scores[persona_name] = score
    
    # Find the persona with the highest score
    if persona_scores:
        best_persona = max(persona_scores, key=persona_scores.get)
        best_score = persona_scores[best_persona]
        
        # If no persona has a meaningful score, default to Job Seeker
        if best_score < 1.0:
            return "Job Seeker"
        
        return best_persona
    
    return "Job Seeker"  # Fallback default
