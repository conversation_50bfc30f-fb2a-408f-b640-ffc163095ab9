# content_modifier.py

from app.utils.model_initializer import model
import hashlib
import time
import re
import emoji
import random

# A private cache for the modification function
_modification_cache = {}

def _count_sentences(text: str) -> int:
    """
    Count the number of sentences in the given text.
    Considers periods, exclamation marks, and question marks as sentence delimiters.
    """
    # Remove HTML tags for accurate sentence counting
    clean_text = re.sub(r'<[^>]+>', '', text)

    # Split by sentence delimiters and filter out empty strings
    sentences = re.split(r'[.!?]+', clean_text)
    sentences = [s.strip() for s in sentences if s.strip()]

    return len(sentences)

def _analyze_html_context(content: str) -> dict:
    """
    Analyze the HTML structure to determine the context for content extension.
    Returns information about the last element and appropriate extension strategy.
    """
    # Remove hashtags for analysis
    clean_content = re.sub(r"<p class=['\"]hashtags['\"]>.*?</p>", "", content, flags=re.DOTALL).strip()

    # Remove trailing <br> tags for cleaner analysis
    clean_content = re.sub(r'(?:\s*<br/?>\s*)+$', '', clean_content, re.IGNORECASE)

    context = {
        'last_element_type': None,
        'is_list': False,
        'is_ordered_list': False,
        'is_paragraph': False,
        'is_blockquote': False,
        'has_emphasis': False,
        'extension_strategy': 'paragraph'  # default
    }

    # Analyze the structure more comprehensively
    if '</ol>' in clean_content:
        # Check if the last meaningful content is an ordered list
        ol_match = re.search(r'<ol[^>]*>.*?</ol>(?:\s*<br/?>\s*)*$', clean_content, re.IGNORECASE | re.DOTALL)
        if ol_match:
            context['last_element_type'] = 'ol'
            context['is_list'] = True
            context['is_ordered_list'] = True
            context['extension_strategy'] = 'ordered_list'

    elif '</ul>' in clean_content:
        # Check if the last meaningful content is an unordered list
        ul_match = re.search(r'<ul[^>]*>.*?</ul>(?:\s*<br/?>\s*)*$', clean_content, re.IGNORECASE | re.DOTALL)
        if ul_match:
            context['last_element_type'] = 'ul'
            context['is_list'] = True
            context['is_ordered_list'] = False
            context['extension_strategy'] = 'unordered_list'

    elif '</blockquote>' in clean_content:
        # Check if the last meaningful content is a blockquote
        bq_match = re.search(r'<blockquote[^>]*>.*?</blockquote>(?:\s*<br/?>\s*)*$', clean_content, re.IGNORECASE | re.DOTALL)
        if bq_match:
            context['last_element_type'] = 'blockquote'
            context['is_blockquote'] = True
            context['extension_strategy'] = 'blockquote'

    elif '</p>' in clean_content:
        # Default case - paragraph
        context['last_element_type'] = 'p'
        context['is_paragraph'] = True
        context['extension_strategy'] = 'paragraph'

        # Check if the last paragraph has emphasis
        last_p_match = re.search(r'<p[^>]*>.*?</p>(?:\s*<br/?>\s*)*$', clean_content, re.IGNORECASE | re.DOTALL)
        if last_p_match and ('<em>' in last_p_match.group() or '<strong>' in last_p_match.group()):
            context['has_emphasis'] = True

    return context

def _extend_content_with_html_preservation(original_content: str, hashtags_html: str, disable_cache: bool, cache_key: str) -> str:
    """
    Extend content while preserving the original HTML structure and using contextually appropriate formatting.
    This function is specifically for the keep_writing feature with intelligent HTML context analysis.
    """
    from app.utils.model_initializer import model

    # Extract the original hashtags paragraph to be appended later
    hashtag_match = re.search(r"(<p class=['\"]hashtags['\"]>.*?</p>)", original_content, flags=re.DOTALL)
    hashtags_html = hashtag_match.group(1) if hashtag_match else ""
    clean_original_content = re.sub(r"<p class=['\"]hashtags['\"]>.*?</p>", "", original_content, flags=re.DOTALL).strip()

    # Step 1: Analyze HTML context to determine extension strategy
    context = _analyze_html_context(clean_original_content)

    # Step 2: Correct grammar and structure in the original content (text only)
    original_text = re.sub(r'<[^>]+>', '', clean_original_content)
    corrected_text = _correct_grammar_and_structure(original_text)

    # Step 3: Count sentences in the corrected content
    sentence_count = _count_sentences(corrected_text)

    # Step 4: Create context-aware extension prompt based on HTML structure
    if context['extension_strategy'] == 'ordered_list':
        extension_prompt = f"""
You are a content extension specialist. Your task is to extend the given HTML content by adding exactly 2 additional list items to the existing ordered list while preserving the original HTML structure EXACTLY.

**--- CRITICAL HTML PRESERVATION RULES ---**
1. **PRESERVE ALL ORIGINAL HTML TAGS**: Keep every single HTML tag from the original content exactly as it is
2. **MAINTAIN STRUCTURE**: Do not change the HTML structure, tag types, or nesting
3. **EXTEND THE LIST**: Add exactly 2 new <li> items to the existing <ol> list
4. **CONSISTENT FORMATTING**: Use the same HTML formatting patterns as the existing list items

**--- EXTENSION REQUIREMENTS ---**
- The content ends with an ordered list (<ol>)
- Add exactly 2 new list items (<li>) that continue the sequence naturally
- Maintain the same tone, style, and formatting as existing list items
- If existing items have emojis or emphasis tags, continue the pattern

**--- ORIGINAL HTML CONTENT TO EXTEND ---**
{clean_original_content}

**--- YOUR TASK ---**
Extend the ordered list by adding exactly 2 additional <li> items that flow naturally from the existing content. Preserve ALL original HTML tags and structure.

**Extended HTML Content:**
"""
    elif context['extension_strategy'] == 'unordered_list':
        extension_prompt = f"""
You are a content extension specialist. Your task is to extend the given HTML content by adding exactly 2 additional list items to the existing unordered list while preserving the original HTML structure EXACTLY.

**--- CRITICAL HTML PRESERVATION RULES ---**
1. **PRESERVE ALL ORIGINAL HTML TAGS**: Keep every single HTML tag from the original content exactly as it is
2. **MAINTAIN STRUCTURE**: Do not change the HTML structure, tag types, or nesting
3. **EXTEND THE LIST**: Add exactly 2 new <li> items to the existing <ul> list
4. **CONSISTENT FORMATTING**: Use the same HTML formatting patterns as the existing list items

**--- EXTENSION REQUIREMENTS ---**
- The content ends with an unordered list (<ul>)
- Add exactly 2 new list items (<li>) that continue the theme naturally
- Maintain the same tone, style, and formatting as existing list items
- If existing items have emojis or emphasis tags, continue the pattern

**--- ORIGINAL HTML CONTENT TO EXTEND ---**
{clean_original_content}

**--- YOUR TASK ---**
Extend the unordered list by adding exactly 2 additional <li> items that flow naturally from the existing content. Preserve ALL original HTML tags and structure.

**Extended HTML Content:**
"""
    elif context['extension_strategy'] == 'blockquote':
        extension_prompt = f"""
You are a content extension specialist. Your task is to extend the given HTML content by adding exactly 2 additional sentences within the existing blockquote while preserving the original HTML structure EXACTLY.

**--- CRITICAL HTML PRESERVATION RULES ---**
1. **PRESERVE ALL ORIGINAL HTML TAGS**: Keep every single HTML tag from the original content exactly as it is
2. **MAINTAIN STRUCTURE**: Do not change the HTML structure, tag types, or nesting
3. **EXTEND WITHIN BLOCKQUOTE**: Add exactly 2 new sentences within the existing <blockquote> element
4. **CONSISTENT FORMATTING**: Use the same HTML formatting patterns as the existing blockquote content

**--- EXTENSION REQUIREMENTS ---**
- The content ends with a blockquote (<blockquote>)
- Add exactly 2 new sentences within the blockquote that continue the thought naturally
- Maintain the same tone, style, and formatting as existing blockquote content

**--- ORIGINAL HTML CONTENT TO EXTEND ---**
{clean_original_content}

**--- YOUR TASK ---**
Extend the blockquote by adding exactly 2 additional sentences within the blockquote element. Preserve ALL original HTML tags and structure.

**Extended HTML Content:**
"""
    else:
        # Default paragraph extension
        target_sentence_count = sentence_count + 2
        extension_prompt = f"""
You are a content extension specialist. Your task is to extend the given HTML content by adding exactly 2 additional sentences while preserving the original HTML structure EXACTLY.

**--- CRITICAL HTML PRESERVATION RULES ---**
1. **PRESERVE ALL ORIGINAL HTML TAGS**: Keep every single HTML tag from the original content exactly as it is
2. **MAINTAIN STRUCTURE**: Do not change the HTML structure, tag types, or nesting
3. **EXTEND NATURALLY**: Add 2 additional sentences that flow naturally from the existing content
4. **CONSISTENT FORMATTING**: If you add new content, use the same HTML formatting patterns as the original

**--- SENTENCE COUNT REQUIREMENT ---**
- Original content has {sentence_count} sentences
- You must output exactly {target_sentence_count} sentences total (original + 2 new)
- Add exactly 2 additional sentences that continue naturally from the existing content

**--- ORIGINAL HTML CONTENT TO EXTEND ---**
{clean_original_content}

**--- YOUR TASK ---**
Extend the above HTML content by adding exactly 2 additional sentences while preserving ALL original HTML tags and structure. The new sentences should flow naturally and maintain the same tone and perspective.

**Extended HTML Content:**
"""

    try:
        response = model.generate_content(extension_prompt, use_cache=(not disable_cache), temperature=0.7)
        extended_content = response.text.strip()

        # Basic cleanup
        extended_content = re.sub(r'^\s*(extended html content:?|result:?)\s*', '', extended_content, flags=re.IGNORECASE)
        extended_content = re.sub(r"^```html\s*|\s*```$", "", extended_content).strip()

        # Re-attach the original hashtags paragraph
        if hashtags_html:
            # Ensure there's a <br> tag before hashtags if content ends with </p>
            if extended_content.rstrip().endswith("</p>"):
                extended_content += f"<br>{hashtags_html}"
            else:
                extended_content += f"\n{hashtags_html}"

        # Cache the result
        if not disable_cache:
            _modification_cache[cache_key] = extended_content

        return extended_content

    except Exception as e:
        print(f"Content extension failed: {e}")
        # Fallback to original content if extension fails
        return original_content

def _correct_grammar_and_structure(text: str) -> str:
    """
    Correct grammar, spelling, and structural issues in the input text.
    This function uses AI to fix common issues while preserving the original intent.
    """
    from app.utils.model_initializer import model

    # Clean the text first
    clean_text = re.sub(r'<[^>]+>', '', text).strip()

    if not clean_text:
        return text

    # Create a focused grammar correction prompt
    correction_prompt = f"""
You are a grammar and writing correction specialist. Your task is to correct the given text while preserving its original meaning and intent.

**--- CORRECTION REQUIREMENTS ---**
1. **Fix Grammar**: Correct grammatical errors and sentence structure issues
2. **Fix Spelling**: Correct any spelling mistakes
3. **Fix Punctuation**: Add missing periods, commas, and other punctuation as needed
4. **Fix Capitalization**: Ensure proper capitalization of first words and proper nouns
5. **Complete Sentences**: If the text appears to be a fragment, complete it to form proper sentences
6. **Preserve Intent**: Keep the original meaning and tone exactly the same
7. **Minimal Changes**: Make only necessary corrections, don't rewrite or expand the content

**--- EXAMPLES ---**
- "i am open to work" → "I am open to work."
- "looking for new job in tech field" → "I am looking for a new job in the tech field."
- "have experience python programming" → "I have experience in Python programming."
- "software engineer with 5 year experience" → "I am a software engineer with 5 years of experience."

**--- TEXT TO CORRECT ---**
{clean_text}

**--- YOUR TASK ---**
Provide only the corrected text. Do not add explanations, comments, or additional content. Just return the grammatically correct version of the input text.

**Corrected Text:**
"""

    try:
        response = model.generate_content(correction_prompt, use_cache=False, temperature=0.3)
        corrected_text = response.text.strip()

        # Clean up any unwanted formatting or extra content
        corrected_text = re.sub(r'^(corrected text:?|result:?)\s*', '', corrected_text, flags=re.IGNORECASE)
        corrected_text = re.sub(r'^["\']|["\']$', '', corrected_text)  # Remove quotes
        corrected_text = corrected_text.strip()

        # Ensure it ends with proper punctuation if it doesn't already
        if corrected_text and not corrected_text[-1] in '.!?':
            corrected_text += '.'

        return corrected_text if corrected_text else clean_text

    except Exception as e:
        # If grammar correction fails, return the original text
        print(f"Grammar correction failed: {e}")
        return clean_text

def _get_modification_cache_key(original_content: str, tone: str, style: str, length: str, keep_writing: bool = False, preserve_length: bool = False) -> str:
    # Generate cache key including keep_writing and preserve_length parameters
    content_hash = hashlib.md5(original_content.encode()).hexdigest()
    params_str = f"rewrite_{tone}_{style}_{length}_{keep_writing}_{preserve_length}"
    params_hash = hashlib.md5(params_str.encode()).hexdigest()
    return f"{content_hash}_{params_hash}"

def modify_content(original_content: str, tone: str = None, style: str = None, length: str = None,
                  keep_writing: bool = False, preserve_length: bool = False, disable_cache: bool = False) -> str:
    """
    Intelligently modifies content length and quality using descriptive AI guidance,
    now with safer long-post limits and mandatory rich text formatting.

    Args:
        original_content: The original content to modify
        tone: The desired tone for the content (optional)
        style: The desired style for the content (optional)
        length: The desired length for the content (optional)
        keep_writing: If True, extends content by adding 2 additional sentences
        preserve_length: If True, skips length modifications and preserves original length
        disable_cache: If True, bypasses caching
    """
    # Handle None parameters by setting defaults only when needed
    if tone is None:
        tone = 'Professional'
    if style is None:
        style = 'Informative'
    if length is None:
        length = 'medium'

    cache_key = _get_modification_cache_key(original_content, tone, style, length, keep_writing, preserve_length)
    
    if not disable_cache and cache_key in _modification_cache:
        return _modification_cache[cache_key]
    
    # Extract the original hashtags paragraph to be appended later
    # Updated regex to handle both single and double quotes around 'hashtags' class
    hashtag_match = re.search(r"(<p class=['\"]hashtags['\"]>.*?</p>)", original_content, flags=re.DOTALL)
    hashtags_html = hashtag_match.group(1) if hashtag_match else ""
    clean_original_content = re.sub(r"<p class=['\"]hashtags['\"]>.*?</p>", "", original_content, flags=re.DOTALL).strip()

    # Handle keep_writing functionality
    if keep_writing:
        # For keep_writing, use a specialized function that preserves HTML structure
        return _extend_content_with_html_preservation(original_content, hashtags_html, disable_cache, cache_key)
    else:
        extension_guidance = ""
    
    length_guidance = ""
    if preserve_length:
        # Calculate content length category for content-length-aware guidance
        content_length = len(clean_original_content)

        # Base length preservation guidance
        length_guidance = f"""
**--- LENGTH PRESERVATION (ABSOLUTE REQUIREMENT) ---**
This is an **ABSOLUTE REQUIREMENT**. You MUST preserve the exact length of the original content.
-   **EXACT LENGTH TARGET:** The output must be approximately {content_length} characters (±20% tolerance maximum).
-   **NO EXPANSION ALLOWED:** Do NOT add extra content, examples, elaboration, emojis, or additional sentences.
-   **NO REDUCTION ALLOWED:** Do NOT remove important content or over-simplify.
-   **TRANSFORMATION ONLY:** Apply ONLY the requested tone/style changes while keeping the same information density.
-   **VERIFICATION:** Before finalizing, count characters and ensure you stay within the target range.
"""

        # Add content-length-aware parameter-specific guidance for problematic combinations
        if style == "Personal storytelling":
            if content_length < 300:  # Short content
                length_guidance += f"""
**--- PERSONAL STORYTELLING - SHORT CONTENT GUIDANCE ---**
CRITICAL: Short content ({content_length} chars) with Personal storytelling style requires EXTREME length discipline:
-   **PREVENT EXPANSION:** Do NOT add narrative elements, backstory, or emotional elaboration
-   **MINIMAL STORYTELLING:** Apply storytelling through word choice and perspective, NOT through adding content
-   **STAY CONCISE:** Transform existing content to storytelling style without expanding length
-   **TARGET: {content_length} characters (±20% = {int(content_length * 0.8)}-{int(content_length * 1.2)} chars)**
"""
            elif content_length > 800:  # Long content
                length_guidance += f"""
**--- PERSONAL STORYTELLING - LONG CONTENT GUIDANCE ---**
CRITICAL: Long content ({content_length} chars) with Personal storytelling style requires COMPRESSION PREVENTION:
-   **PRESERVE DETAIL:** Do NOT remove important details or compress the narrative
-   **MAINTAIN RICHNESS:** Keep the existing depth while applying storytelling perspective
-   **NO OVER-SIMPLIFICATION:** Transform tone/style without reducing information density
-   **TARGET: {content_length} characters (±20% = {int(content_length * 0.8)}-{int(content_length * 1.2)} chars)**
"""

        if tone == "Enthusiastic" and 400 <= content_length <= 800:  # Medium content
            length_guidance += f"""
**--- ENTHUSIASTIC TONE - MEDIUM CONTENT GUIDANCE ---**
CRITICAL: Medium content ({content_length} chars) with Enthusiastic tone requires COMPRESSION PREVENTION:
-   **MAINTAIN ENERGY WITHOUT COMPRESSION:** Apply enthusiasm through word choice, NOT by removing content
-   **PRESERVE ALL DETAILS:** Keep all key information while making it more enthusiastic
-   **NO OVER-CONDENSATION:** Enthusiasm should enhance, not compress the message
-   **TARGET: {content_length} characters (±20% = {int(content_length * 0.8)}-{int(content_length * 1.2)} chars)**
"""

        if tone == "Friendly" and content_length > 800:  # Long content
            length_guidance += f"""
**--- FRIENDLY TONE - LONG CONTENT GUIDANCE ---**
CRITICAL: Long content ({content_length} chars) with Friendly tone requires COMPRESSION PREVENTION:
-   **MAINTAIN FRIENDLINESS WITHOUT SIMPLIFICATION:** Apply friendly tone while preserving all content
-   **KEEP COMPREHENSIVE DETAILS:** Friendly does not mean brief - maintain the full message
-   **NO OVER-CASUALIZATION:** Make it friendly without removing important information
-   **TARGET: {content_length} characters (±20% = {int(content_length * 0.8)}-{int(content_length * 1.2)} chars)**
"""
    elif length == "short":
        length_guidance = "This is a **CRITICAL** instruction. The post **MUST BE SHORT** (under 600 characters). Be extremely concise. The Hook must be a single, powerful sentence. The Body should be 2-3 sentences max."
    elif length == "medium":
        length_guidance = """
This is a **CRITICAL** instruction. The post must be **MEDIUM** length (under 1500 characters). Provide a good balance of detail and readability.
-   **Paragraph Structure (CRITICAL):** Do NOT use dense paragraphs. Keep most paragraphs to 2-3 sentences. Use single-sentence paragraphs for emphasis. The goal is a scannable, visually appealing post.
-   **Body:** The Body should be well-developed with clear paragraphs and/or a bulleted list.
"""
    elif length == "long":
        length_guidance = """
This is a **CRITICAL** instruction. The post must be a **LONG**, in-depth piece (above 1500 characters and under 2,500 characters).
-   **Character Limit (ABSOLUTE RULE):** The final post, including all HTML and text, MUST NOT exceed 3,000 characters. This is the maximum length for a LinkedIn post.
-   **Paragraph Structure (CRITICAL):** It is absolutely essential to avoid dense paragraphs. The post MUST be highly scannable on the LinkedIn feed.
    -   Keep paragraphs short (2-3 sentences max).
    -   Use one-sentence lines to break up text and add impact.
    -   Utilize whitespace effectively to guide the reader's eye.
-   **Content Goal:** The post must be comprehensive and establish expertise, but its formatting must prioritize readability.
"""

    random_seed = f"{int(time.time() * 1000)}_{random.randint(1000, 9999)}"

    # Calculate content length for content-length-aware guidance
    content_length = len(clean_original_content)

    tone_and_style_guidance = ""
    if tone == 'Professional' and style == 'Informative':
        tone_and_style_guidance = """
**--- TONE & STYLE GUIDANCE ---**
-   **Preserve Original Voice:** You MUST analyze the original text's tone and style and preserve it in the rewritten version. The goal is to enhance the post, not to change the author's original voice.
"""
    else:
        tone_and_style_guidance = f"""
**--- TONE & STYLE GUIDANCE (CRITICAL) ---**
You MUST adopt the following persona and writing style for the entire post:
-   **Tone:** {tone}
-   **Style:** {style}
"""

        # Add content-length-aware parameter-specific guidance for problematic combinations
        # (when preserve_length is False, we still want to be mindful of these issues)
        if not preserve_length:
            if style == "Personal storytelling":
                if content_length < 300:  # Short content
                    tone_and_style_guidance += f"""
**--- PERSONAL STORYTELLING - SHORT CONTENT AWARENESS ---**
IMPORTANT: Original content is short ({content_length} chars). When applying storytelling style:
-   **CONTROLLED EXPANSION:** If expanding for storytelling, do so moderately and purposefully
-   **NARRATIVE EFFICIENCY:** Use storytelling techniques that enhance without excessive elaboration
-   **MAINTAIN FOCUS:** Keep the core message clear and concise even with storytelling elements
"""
                elif content_length > 800:  # Long content
                    tone_and_style_guidance += f"""
**--- PERSONAL STORYTELLING - LONG CONTENT AWARENESS ---**
IMPORTANT: Original content is comprehensive ({content_length} chars). When applying storytelling style:
-   **PRESERVE RICHNESS:** Maintain the depth and detail while adding storytelling perspective
-   **NARRATIVE ENHANCEMENT:** Use storytelling to enhance existing content, not replace it
-   **KEEP COMPREHENSIVE:** Storytelling should add perspective, not reduce information
"""

            if tone == "Enthusiastic" and 400 <= content_length <= 800:  # Medium content
                tone_and_style_guidance += f"""
**--- ENTHUSIASTIC TONE - MEDIUM CONTENT AWARENESS ---**
IMPORTANT: Original content is medium-length ({content_length} chars). When applying enthusiastic tone:
-   **ENERGY WITH SUBSTANCE:** Apply enthusiasm while maintaining all key information
-   **AVOID OVER-COMPRESSION:** Enthusiasm should enhance, not condense the message
-   **PRESERVE DETAILS:** Keep all important details while making them more engaging
"""

            if tone == "Friendly" and content_length > 800:  # Long content
                tone_and_style_guidance += f"""
**--- FRIENDLY TONE - LONG CONTENT AWARENESS ---**
IMPORTANT: Original content is comprehensive ({content_length} chars). When applying friendly tone:
-   **FRIENDLY COMPLETENESS:** Maintain all information while making it more approachable
-   **WARM BUT THOROUGH:** Friendly tone should not mean less comprehensive
-   **PRESERVE DEPTH:** Keep the full scope of information with a friendlier delivery
"""

    if keep_writing:
        prompt_template = f"""
You are a content extension specialist. Your task is to extend the given text by adding exactly 2 additional sentences that naturally continue the thought.

**--- EXTENSION REQUIREMENTS (CRITICAL) ---**
1. **Preserve Base Content:** Keep the provided text exactly as given - do NOT rewrite, rephrase, or restructure it. This text has already been grammar-corrected.
2. **Add Exactly 2 Sentences:** Add precisely 2 additional sentences that continue the thought naturally.
3. **Maintain Consistency:** The additional sentences must maintain the same tone, style, and perspective as the base text.
4. **Natural Flow:** The extension should feel like a natural continuation, not an afterthought.
5. **Simple HTML:** Wrap the entire result in a single `<p>` tag. Do NOT use complex formatting, lists, or multiple paragraphs.

{extension_guidance}

**--- GRAMMAR-CORRECTED TEXT TO EXTEND ---**
{clean_original_content}

**--- YOUR TASK ---**
Extend the above text by adding exactly 2 sentences that continue the thought naturally. Output the original text plus the 2 additional sentences in a single `<p>` tag.

**Final Extended Text (HTML):**
"""
    else:
        prompt_template = f"""
You are an expert LinkedIn content strategist and copywriter. Your mission is to transform the raw text below into a complete, professional, and engaging LinkedIn post, strictly adhering to all instructions. Your final output MUST be in rich text HTML format.

**--- CORE REQUIREMENTS ---**
1.  **Rewrite Intelligently:** You MUST rewrite the entire original text. Do not simply reformat it. Preserve the core message while adapting it to the requested length, tone, and style.
2.  **Unique Hook on Every Hit:** You MUST write a fresh, new 'Hook' for the post on every request. Use the randomization seed to ensure you generate a unique output.
3.  **Strict Structure:** The final post MUST seamlessly integrate a Hook, a Body, and a Call to Action without using those labels. The structure should feel natural to the reader. **Crucially, there MUST be a line break between the hook and the rest of the post.**
4.  **Bold Hook (CRITICAL):** The Hook (the first paragraph) MUST be wrapped entirely in `<strong>` tags.
5.  **Contextual CTA (CRITICAL):** The Call to Action (the final paragraph) MUST be contextually relevant to the content, asking a thoughtful question or prompting a specific, logical next step that encourages meaningful engagement.

{tone_and_style_guidance}

**--- WRITING STYLE (CRITICAL) ---**
-   **Conversational Flow:** The post must flow like a spoken conversation.
-   **Simple Vocabulary:** Use simple, everyday words. Avoid words over 3 syllables unless they are common.
-   **Varied Sentences:** Vary sentence lengths. Mix short, punchy sentences with longer, more descriptive ones.
-   **Natural Language:** Incorporate natural touches like contractions (e.g., "it's," "you're"), idioms, and a touch of humor or empathy where appropriate.

**--- LENGTH GUIDANCE (CRITICAL) ---**
-   **Target Length:** {length.capitalize()}
-   **Instructions:** {length_guidance}

**--- RICH TEXT FORMATTING RULES (CRITICAL) ---**
-   **HTML Structure:** Your entire output MUST be valid HTML. You MUST wrap distinct ideas or paragraphs in `<p>` tags for proper structure and line breaks. Do not return plain text.
-   **Emoji Bullet Points:** For any series of items, benefits, or features, you MUST use an emoji-bulleted list.
    -   **CRITICAL STYLING:** To prevent double bullets (the browser's bullet next to the emoji), the list tag MUST be written exactly as: `<ul style="list-style-type: none;">`.
    -   Each list item within that `<ul>` MUST begin with a relevant, professional emoji (e.g., ✅, 💡, 👉, 🔹) followed by the text, like this: `<li>✅ Your text here</li>`.
-   **VALID HTML:** Do NOT place `<br>` or `<br/>` tags inside a `<ul>` or `<ol>` tag, or between `<li>` elements.
-   **Numbered Lists:** For sequential steps, you may use a standard `<ol><li>` list.
-   **Emphasize Key Points:** Use `<strong>` or `<b>` tags to bold 2-4 key phrases. Use `<em>` or `<i>` for italics.
-   **Allowed HTML Tags:** Only use: `<p>`, `<strong>`, `<b>`, `<em>`, `<i>`, `<ul style="list-style-type: none;">`, `<ol>`, `<li>`, `<blockquote>`.

**--- ORIGINAL RAW TEXT TO TRANSFORM ---**
{clean_original_content}

**--- YOUR TASK ---**
Now, generate the complete, rewritten LinkedIn post in clean, well-structured HTML format. It must strictly follow all length, tone, style, and formatting rules, especially the **bold hook**, **contextual CTA**, and **paragraph structure** rules.
{f"**CRITICAL LENGTH REMINDER:** You MUST keep the output to approximately {len(clean_original_content)} characters. Do NOT exceed this target by more than 20%." if preserve_length else ""}

**Randomization Seed (for ensuring a unique hook):** {random_seed}

**Final LinkedIn Post (HTML):**
"""

    response = model.generate_content(prompt_template, use_cache=(not disable_cache), temperature=0.8)
    modified_content = response.text.strip()
    
    # Basic cleanup
    modified_content = re.sub(r'\(\s*seed\s*:\s*\d+_\d+\s*\)', '', modified_content, flags=re.IGNORECASE).strip()
    modified_content = re.sub(r'^\s*(hook|the hook|body|the body|call to action|cta)\s*[:\-–—]?\s*', '', modified_content, flags=re.IGNORECASE | re.MULTILINE).strip()
    modified_content = re.sub(r"^```html\s*|\s*```$", "", modified_content).strip()

    # Re-attach the original hashtags paragraph before final formatting
    if hashtags_html:
        # Ensure there's a <br> tag before hashtags if content ends with </p>
        if modified_content.rstrip().endswith("</p>"):
            modified_content += f"<br>{hashtags_html}"
        else:
            modified_content += f"\n{hashtags_html}"

    if keep_writing:
        # For keep_writing, preserve the original HTML structure
        # The AI should have already generated content with proper HTML formatting
        # No additional processing needed to maintain structure
        pass
    else:
        # --- START: REVISED HTML CLEANUP AND FORMATTING ---

        # 1. Clean any invalid <br> tags from within lists first.
        def remove_br_in_lists(match):
            list_content = match.group(0)
            return re.sub(r'<br\s*/?>\s*', '', list_content)

        modified_content = re.sub(r'<ul[^>]*>.*?</ul>', remove_br_in_lists, modified_content, flags=re.DOTALL)
        modified_content = re.sub(r'<ol[^>]*>.*?</ol>', remove_br_in_lists, modified_content, flags=re.DOTALL)
        
        # 2. Add <br> tags between adjacent paragraphs for proper spacing.
        # This now correctly handles the space before the hashtags paragraph.
        modified_content = re.sub(r'(</p>)\s*(<p)', r'\1<br>\2', modified_content)

        # 3. Enforce the bold hook as a safeguard.
        def bold_first_paragraph(match):
            p_tag_open = match.group(1)
            content = match.group(2)
            p_tag_close = match.group(3)

            content = content.strip()
            if content.startswith('<strong>') and content.endswith('</strong>'):
                return match.group(0)

            return f"{p_tag_open}<strong>{content}</strong>{p_tag_close}"

        modified_content = re.sub(r'(<p.*?>)(.*?)(</p>)', bold_first_paragraph, modified_content, count=1, flags=re.DOTALL)
        # --- END: REVISED HTML CLEANUP AND FORMATTING ---
  
    if not disable_cache:
        _modification_cache[cache_key] = modified_content
    
    return modified_content

def get_suggested_emojis(text: str, length: str = 'medium') -> list:
    # This function is unchanged
    if not text:
        return []

    length = length.lower()
    if length == 'long':
        emoji_count = random.randint(5, 8)
        count_instruction = f"Suggest exactly {emoji_count} emojis for this long, detailed post."
    elif length == 'short':
        emoji_count = random.randint(2, 4)
        count_instruction = f"Suggest {emoji_count} emojis for this short, concise post."
    else: # Medium
        emoji_count = random.randint(3, 5)
        count_instruction = f"Suggest {emoji_count} emojis for this medium-length post."

    random_seed = f"{int(time.time() * 1000)}_{random.randint(1000, 9999)}"
    clean_text = re.sub(r'<[^>]+>', '', text).strip()
    
    prompt = f"""
Analyze the professional content below for its core themes and tone.
{count_instruction} The emojis must be highly relevant and professional.
**CRITICAL REQUIREMENT:** You MUST provide a different and varied set of emojis on every request. Use the randomization seed below to ensure your output is unique.
**Content for Analysis:**
"{clean_text}"
**Randomization Seed (for ensuring unique output):** {random_seed}
**Instructions:**
- Return ONLY the emoji characters, separated by a single space. Do not include any other text.
**Suggested Emojis:**
"""
    response = model.generate_content(prompt, temperature=0.85, use_cache=False)
    emoji_string = response.text.strip()
    
    return [char for char in emoji_string if char in emoji.EMOJI_DATA]

def _contains_emoji(s: str) -> bool:
    """Helper function to check if a string contains any emoji."""
    return any(char in emoji.EMOJI_DATA for char in s)

def insert_emojis_into_html(html_text: str, emojis: list) -> str:
    """
    Inserts emojis into an HTML string, adding one emoji per paragraph
    to paragraphs that do not already contain one. It will not add
    excess emojis if there are no available paragraphs.
    """
    if not emojis or not html_text:
        return html_text

    paragraphs = re.findall(r'(<p.*?>.*?</p>)', html_text, re.DOTALL)
    if not paragraphs:
        return f"{html_text} {emojis[0]}"

    emojis_to_add = emojis[:]
    
    temp_html_text = html_text

    for para in paragraphs:
        if not emojis_to_add:
            break
        
        if not _contains_emoji(para):
            emoji_to_insert = emojis_to_add.pop(0)
            modified_para = para.replace("</p>", f" {emoji_to_insert}</p>", 1)
            temp_html_text = temp_html_text.replace(para, modified_para, 1)
    
    return temp_html_text

def clear_modification_cache():
    # This function is unchanged
    global _modification_cache
    _modification_cache.clear()


def _rephrase_text_chunk(text_chunk: str) -> str:
    """
    Uses the AI model to rephrase a single chunk of plain text.
    Ensures a unique response on each call.
    """
    if not text_chunk.strip():
        return text_chunk

    prompt = f"""Rephrase this text using simple, everyday words. Keep the same meaning and length.

RULES:
- Use simple words (1-3 syllables)
- Keep the same meaning
- Keep approximately the same length
- Use contractions (it's, don't, we'll)
- Return ONLY the rephrased text
- Do NOT add explanations, instructions, or extra content

TEXT TO REPHRASE:
{text_chunk}

REPHRASED TEXT:"""
    try:
        # use_cache=False and a low temperature for more consistent results
        response = model.generate_content(prompt, use_cache=False, temperature=0.3)
        rephrased_text = response.text.strip()

        # Clean up any unwanted prefixes, suffixes, or formatting
        rephrased_text = re.sub(r'^(rephrased text:?|result:?|here\'s the rephrased text:?)\s*', '', rephrased_text, flags=re.IGNORECASE)
        rephrased_text = re.sub(r'^["\']|["\']$', '', rephrased_text)  # Remove quotes
        rephrased_text = re.sub(r'^\*\*.*?\*\*\s*', '', rephrased_text)  # Remove bold formatting
        rephrased_text = re.sub(r'^-+\s*', '', rephrased_text)  # Remove dashes
        rephrased_text = rephrased_text.strip()

        # Validation: Check if the response is reasonable
        original_length = len(text_chunk.strip())
        rephrased_length = len(rephrased_text)

        # If the rephrased text is too long (more than 3x original) or contains instructions/explanations, fallback
        if (rephrased_length > original_length * 3 or
            any(phrase in rephrased_text.lower() for phrase in [
                'here\'s what', 'you need', 'instructions', 'requirements',
                'critical', 'important', 'note that', 'remember to'
            ])):
            print(f"AI response too long or contains instructions, using fallback for: {text_chunk[:50]}...")
            return text_chunk

        return rephrased_text if rephrased_text else text_chunk
    except Exception as e:
        print(f"Content rephrasing failed for chunk: {e}")
        # Fallback to original text if AI call fails
        return text_chunk

def rephrase_content(original_content: str) -> str:
    """
    Rephrases content while preserving its HTML structure exactly.
    It identifies text within HTML tags, rephrases it, and reconstructs the HTML.
    This ensures a unique rephrase on every execution while maintaining identical HTML structure.
    """
    if not original_content:
        return ""

    # Extract original HTML tags for validation
    original_tags = re.findall(r'<[^>]+>', original_content)

    # Split the content by HTML tags, keeping the tags as delimiters in the list
    parts = re.split(r'(<[^>]+>)', original_content)

    rephrased_parts = []
    for part in parts:
        # If the part is an HTML tag or just whitespace, append it without change
        if re.match(r'^\s*<[^>]+>\s*$', part) or not part.strip():
            rephrased_parts.append(part)
        else:
            # This is a text node; rephrase it only if it has meaningful content
            if len(part.strip()) > 2:  # Only rephrase meaningful text chunks
                rephrased_text = _rephrase_text_chunk(part)
                rephrased_parts.append(rephrased_text)
            else:
                # Keep short text chunks (like spaces, punctuation) unchanged
                rephrased_parts.append(part)

    # Join the parts back together to form the final HTML
    result = "".join(rephrased_parts)

    # Validation: Ensure HTML structure is preserved
    result_tags = re.findall(r'<[^>]+>', result)
    if len(result_tags) != len(original_tags):
        print(f"Warning: HTML tag count mismatch. Original: {len(original_tags)}, Result: {len(result_tags)}")
        print(f"Original tags: {original_tags}")
        print(f"Result tags: {result_tags}")
        # If structure is broken, return original content
        return original_content

    return result